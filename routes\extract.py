import logging
from textExtraction import load_pdf, getOCRed, load_image
from services.llm import preparePrompt, getResponse
import constants
import json
import pandas as pd
import xml.etree.ElementTree as ET


def extractInvoice(file):
    file_contents = file.read()
    logging.info("Extracting Text.")
    if file.filename.lower().endswith(".pdf"):
        pages_text, ocr_pages, page_numbers = load_pdf(file_contents)
    else:
        pages_text, ocr_pages, page_numbers = load_image(file_contents)
    pages_text, raw_text = getOCRed(pages_text, ocr_pages, page_numbers)
    logging.info("Text Extracted.")
    response = getLLMResult(raw_text)
    logging.info("Got LLM response.")
    return response, pages_text


def convert_text_to_dict(response):
    try:
        return json.loads(response)
    except:
        return eval(response)


def format_vat(response):
    response["Provider VAT ID"] = (
        response["Provider VAT ID"].replace(" ", "").replace("/", "")
    )
    return response


def getLLMResult(raw_text):
    prompt = preparePrompt(raw_text)
    result = getResponse(prompt)
    response = convert_text_to_dict(result.choices[0].message.content)
    response = format_vat(response)
    logging.info(f"LLM Response: {response}")
    if "Type" in response and response["Type"] in constants.CODES["Code"]:
        response["Code 1"] = constants.CODES["Code"][response["Type"]]
        response["Subcode"] = constants.CODES["Subcode"][response["Type"]]
    elif "type" in response and response["type"] in constants.CODES["Code"]:
        response["Code 1"] = constants.CODES["Code"][response["type"]]
        response["Subcode"] = constants.CODES["Subcode"][response["type"]]
    else:
        response["Code 1"] = ""
        response["Subcode"] = ""
    return response


def create_xml_from_excel(excel_file_content):

    ns = {
        "etd": "http://crd.gov.pl/xml/schematy/dziedzinowe/mf/2021/06/08/eD/DefinicjeTypy/"
    }
    ET.register_namespace("etd", ns["etd"])

    # Load data from Excel
    data = pd.read_excel(excel_file_content, sheet_name="Other").iloc[0]
    df_invoices = pd.read_excel(excel_file_content, sheet_name="Invoices")

    # Load all Others Once
    KodFormularza = data["KodFormularza"]
    WariantFormularza = str(data["WariantFormularza"])
    DataWypelnienia = str(data["DataWypelnienia"])
    CelZlozenia = str(data["CelZlozenia"])
    KodUrzedu = str(data["KodUrzedu"])
    NIP = str(data["NIP"])
    PelnaNazwa = str(data["PelnaNazwa"])
    KodKraju = str(data["KodKraju"])
    Wojewodztwo = str(data["Wojewodztwo"])
    Powiat = str(data["Powiat"])
    Gmina = str(data["Gmina"])
    Ulica = str(data["Ulica"])
    NrDomu = str(data["NrDomu"])
    Miejscowosc = str(data["Miejscowosc"])
    KodPocztowy = str(data["KodPocztowy"])
    Poczta = str(data["Poczta"])
    Email = str(data["Email"])
    RefundingCountryCode = str(data["RefundingCountryCode"])
    StartDate = str(data["StartDate"])
    EndDate = str(data["EndDate"])
    BusinessActivity = str(data["BusinessActivity"])
    OwnerName = str(data["OwnerName"])
    OwnerType = str(data["OwnerType"])
    IBAN = str(data["IBAN"])
    BIC = str(data["BIC"])

    root = ET.Element("Wniosek", xmlns="http://crd.gov.pl/wzor/2025/03/17/13717/")
    # Naglowek
    naglowek = ET.SubElement(root, "Naglowek")
    ET.SubElement(
        naglowek, "KodFormularza", kodSystemowy=KodFormularza, wersjaSchemy="1-0E"
    ).text = "VAT-REF"
    ET.SubElement(naglowek, "WariantFormularza").text = WariantFormularza
    ET.SubElement(naglowek, "DataWypelnienia").text = DataWypelnienia
    ET.SubElement(naglowek, "CelZlozenia").text = CelZlozenia
    ET.SubElement(naglowek, "KodUrzedu").text = KodUrzedu

    # Podmiot1 (Podatnik)
    podmiot1 = ET.SubElement(root, "Podmiot1", rola="Podatnik")
    osoba = ET.SubElement(podmiot1, ET.QName(ns["etd"], "OsobaNiefizyczna"))
    ET.SubElement(osoba, ET.QName(ns["etd"], "NIP")).text = NIP
    ET.SubElement(osoba, ET.QName(ns["etd"], "PelnaNazwa")).text = PelnaNazwa

    adres1 = ET.SubElement(podmiot1, "AdresZamieszkaniaSiedziby", rodzajAdresu="RAD")
    adres_pol = ET.SubElement(adres1, "AdresPol")
    ET.SubElement(adres_pol, ET.QName(ns["etd"], "KodKraju")).text = KodKraju
    ET.SubElement(adres_pol, ET.QName(ns["etd"], "Wojewodztwo")).text = Wojewodztwo
    ET.SubElement(adres_pol, ET.QName(ns["etd"], "Powiat")).text = Powiat
    ET.SubElement(adres_pol, ET.QName(ns["etd"], "Gmina")).text = Gmina
    ET.SubElement(adres_pol, ET.QName(ns["etd"], "Ulica")).text = Ulica
    ET.SubElement(adres_pol, ET.QName(ns["etd"], "NrDomu")).text = NrDomu
    ET.SubElement(adres_pol, ET.QName(ns["etd"], "Miejscowosc")).text = Miejscowosc
    ET.SubElement(adres_pol, ET.QName(ns["etd"], "KodPocztowy")).text = KodPocztowy
    ET.SubElement(adres_pol, ET.QName(ns["etd"], "Poczta")).text = Poczta

    ET.SubElement(podmiot1, "Email").text = Email

    # VAT Refund Application
    details = ET.SubElement(root, "Details")
    vat_refund = ET.SubElement(details, "VATRefundApplication")
    ET.SubElement(vat_refund, "RefundingCountryCode").text = RefundingCountryCode

    refund_period = ET.SubElement(vat_refund, "RefundPeriod")
    ET.SubElement(refund_period, "StartDate").text = StartDate
    ET.SubElement(refund_period, "EndDate").text = EndDate

    business_desc = ET.SubElement(vat_refund, "BusinessDescription")
    ET.SubElement(business_desc, "BusinessActivity").text = BusinessActivity

    bank_account = ET.SubElement(vat_refund, "DetailedBankAccount")
    ET.SubElement(bank_account, "OwnerName").text = OwnerName
    ET.SubElement(bank_account, "OwnerType").text = OwnerType
    ET.SubElement(bank_account, "IBAN").text = IBAN
    ET.SubElement(bank_account, "BIC").text = BIC
    ET.SubElement(bank_account, "Currency").text = str(data["Currency"])

    for index, data in df_invoices.iterrows():
        # Purchase Information
        purchase_info = ET.SubElement(
            vat_refund, "PurchaseInformation", {"simplifiedInvoice": "0"}
        )
        ET.SubElement(purchase_info, "SequenceNumber").text = str(
            data["68. SequenceNumber"]
        )
        ET.SubElement(purchase_info, "ReferenceNumber").text = str(
            data["69. Invoice No"]
        )
        ET.SubElement(purchase_info, "IssuingDate").text = str(
            data["71. Issue date"]
        ).replace("-", ".")

        supplier = ET.SubElement(purchase_info, "EUSupplier")
        ET.SubElement(supplier, "NameFree").text = str(data["72. Provider name"])
        ET.SubElement(supplier, "AddressFree").text = str(data["73. Provider address"])
        ET.SubElement(supplier, "CountryCode").text = str(data["75. Provider country"])
        trader_id = ET.SubElement(supplier, "EUTraderID")
        ET.SubElement(
            trader_id,
            "VATIdentificationNumber",
            {"issuedBy": str(data["75. Provider country"])},
        ).text = str(data["76. Provider VAT ID"])

        goods_description = ET.SubElement(purchase_info, "GoodsDescription")
        ET.SubElement(goods_description, "Code").text = str(data["78. Code 1"])
        ET.SubElement(goods_description, "SubCode").text = str(data["79. Subcode"])

        ET.SubElement(purchase_info, "Currency").text = str(data["82. Currency"])

        transaction_description = ET.SubElement(purchase_info, "TransactionDescription")
        ET.SubElement(transaction_description, "TaxableAmount").text = str(
            data["83. Gross / Brutto"]
        )
        ET.SubElement(transaction_description, "VATAmount").text = str(data["84. VAT"])

        deduction = ET.SubElement(purchase_info, "Deduction")
        ET.SubElement(deduction, "DeductibleVATAmount").text = str(
            data["86. Net / Netto"]
        )

    zalaczniki = ET.SubElement(vat_refund, "Zalaczniki")
    InfOKopiiDokumentu = ET.SubElement(zalaczniki, "InfOKopiiDokumentu")
    ET.SubElement(InfOKopiiDokumentu, "TypPliku").text = "application/pdf"
    ET.SubElement(InfOKopiiDokumentu, "NazwaPliku").text = "1.pdf"

    oswiadczenie = ET.SubElement(root, "Oswiadczenie1")
    oswiadczenie.text = "Oświadczam, że w państwie członkowskim, do którego kierowany jest wniosek w okresie, do którego odnosi się wniosek - nie dokonywałem dostaw towarów lub świadczenia usług, z wyjątkiem: a) usług transportu i usług pomocniczych do takich usług, zwolnionych zgodnie z przepisami państwa członkowskiego zwrotu implementującymi przepisy art. 144, 146, 148, 149, 151, 153, 159 lub 160 dyrektywy 2006/112/WE; b) usług, wewnątrzwspólnotowej sprzedaży towarów na odległość i dostaw towarów dokonywanych przez podatnika ułatwiającego takie dostawy zgodnie z art. 14a ust. 2 dyrektywy 2006/112/WE, rozliczanych według procedury szczególnej określonej w tytule XII w rozdziale 6 w sekcji 3 tej dyrektywy; c) sprzedaży na odległość towarów importowanych rozliczanej według procedury szczególnej określonej w tytule XII w rozdziale 6 w sekcji 4 dyrektywy 2006/112/WE; d) dostaw towarów lub świadczenia usług na rzecz osoby zobowiązanej do zapłaty podatku od wartości dodanej zgodnie z przepisami państwa członkowskiego zwrotu implementującymi przepisy art. 194 – 197 i art. 199 dyrektywy 2006/112/WE"

    zgoda = ET.SubElement(root, "Zgoda")
    zgoda.text = "Wyrażam zgodę na otrzymywanie pism (informacji) w sprawie wniosku o zwrot podatku od wartości dodanej za pomocą środków komunikacji elektronicznej, na podany we wniosku adres poczty elektronicznej."

    pouczenie = ET.SubElement(root, "Pouczenie")
    pouczenie.text = "Za podanie nieprawdy lub zatajenie prawdy grozi odpowiedzialność przewidziana w Kodeksie karnym skarbowym."

    tree = ET.ElementTree(root)
    return tree
