from azure.core.credentials import AzureKeyCredential
from azure.ai.formrecognizer import DocumentAnalysisClient
import constants
from io import BytesIO
import logging


def get_extracted_text_from_image(image: bytes) -> tuple:
    document_analysis_client = DocumentAnalysisClient(
        endpoint=constants.AZURE_VISION_AI_OCR_ENDPOINT,
        credential=AzureKeyCredential(constants.AZURE_VISION_AI_API_KEYS[0]),
    )
    poller = document_analysis_client.begin_analyze_document(
        model_id="prebuilt-read", document=image
    )
    logging.info("Extracted text for the page.")
    return poller.result().to_dict()


def get_formatted_ocr_response(document_text_json) -> dict:
    image_data = document_text_json["pages"][0]
    return {
        "content": document_text_json["content"],
        "info": format_azure_data(image_data["words"]),
        "image_size": {
            "width": int(image_data["width"]),
            "height": int(image_data["height"]),
        },
        "angle": image_data["angle"],
        "extraction": "Azure",
    }


def format_azure_data(words_data: list) -> list:
    word_box_info = []
    for word in words_data:
        vertices_bb = []
        for point in word["polygon"]:
            x = int((point["x"]))
            y = int(point["y"])
            vertices_bb.append((x, y))
        if len(vertices_bb) > 0:
            left_most = min(vertices_bb[0][0], vertices_bb[3][0])
            right_most = max(vertices_bb[1][0], vertices_bb[2][0])
            top_most = min(vertices_bb[0][1], vertices_bb[2][1])
            bottom_most = max(vertices_bb[2][1], vertices_bb[3][1])
            word_box_info.append(
                [[left_most, top_most, right_most, bottom_most], word["content"]]
            )
    return word_box_info


def extractAzureOCRText(images, page_numbers):

    ocr_text = {}

    for img, page in zip(images, page_numbers):
        img_byte_arr = BytesIO()
        img.save(img_byte_arr, format="PNG")
        img_new = img_byte_arr.getvalue()
        ocr_text[page] = get_formatted_ocr_response(
            get_extracted_text_from_image(img_new)
        )

    return ocr_text
