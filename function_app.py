from azure import functions as func
import logging
from routes.extract import extractInvoice, create_xml_from_excel
import json
import pandas as pd
import io
from constants import AUTHENTICATION_SECRET
import zipfile

app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)


@app.route(route="upload")
def upload(req: func.HttpRequest) -> func.HttpResponse:
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>File Uploader</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #eef2f7;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
            }
            .container {
                background-color: #fff;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                text-align: center;
                width: 450px;
            }
            h1 {
                font-size: 28px;
                margin-bottom: 20px;
                color: #2c3e50;
            }
            label {
                font-size: 16px;
                color: #34495e;
            }
            input[type="file"],
            input[type="text"] {
                display: block;
                margin: 20px auto;
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
                width: 100%;
                font-size: 14px;
            }
            input[type="submit"] {
                background-color: #3498db;
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 5px;
                font-size: 16px;
                cursor: pointer;
                margin-top: 10px;
                width: 100%;
            }
            input[type="submit"]:hover {
                background-color: #2980b9;
            }
            .spinner {
                display: none;
                margin-top: 20px;
            }
            .spinner div {
                width: 18px;
                height: 18px;
                background-color: #3498db;
                border-radius: 100%;
                display: inline-block;
                animation: bounce 1.4s infinite ease-in-out both;
            }
            .spinner .bounce1 {
                animation-delay: -0.32s;
            }
            .spinner .bounce2 {
                animation-delay: -0.16s;
            }
            @keyframes bounce {
                0%, 100% {
                    transform: scale(0.0);
                }
                50% {
                    transform: scale(1.0);
                }
            }
            .progress-bar {
                width: 100%;
                background-color: #f3f3f3;
                border-radius: 5px;
                overflow: hidden;
                margin-top: 10px;
                display: none;
            }
            .progress {
                height: 12px;
                width: 0;
                background-color: #2ecc71;
                transition: width 0.4s;
            }
        </style>
        <script>
            function setActionAndKey(actionType, actionUrl) {
                const key = document.getElementById("key").value;
                if (!key) {
                    alert("Please enter a valid KEY.");
                    return false;
                }
                document.getElementById("action").value = actionType;  // Set action type
                document.getElementById("file-form").action = actionUrl + "?key=" + encodeURIComponent(key);  // Append KEY to URL
                return true;
            }

            function showSpinner() {
                document.getElementById("spinner").style.display = "block";
                document.getElementById("progress-bar").style.display = "block";
                setTimeout(() => {
                    document.querySelector(".progress").style.width = "100%";
                }, 100);
            }
        </script>
    </head>
    <body>
        <div class="container">
            <h1>Upload Files</h1>
            <form id="file-form" method="post" enctype="multipart/form-data" onsubmit="return setActionAndKey(actionType, actionUrl)">
                <label for="key">Enter KEY:</label>
                <input type="text" id="key" name="key" placeholder="Enter your API key here" required>

                <label for="files">Select Files (PDF, PNG, JPEG):</label>
                <input type="file" id="files" name="files" accept=".pdf, .jpg, .jpeg, .png" multiple required>
                
                <input type="hidden" name="action" id="action">

                <input type="submit" value="Submit" onclick="return setActionAndKey('submit', '/api/extract-invoice')">
                <input type="submit" value="Download Excel File" onclick="return setActionAndKey('download', '/api/download-excel')">
            </form>

            <div class="progress-bar" id="progress-bar">
                <div class="progress"></div>
            </div>
            <div class="spinner" id="spinner">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
            </div>
        </div>
    </body>
    </html>
    """
    return func.HttpResponse(html_content, mimetype="text/html")


@app.route(route="convert-excel-to-xml")
def convert_excel_to_xml(req: func.HttpRequest) -> func.HttpResponse:
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Excel-to-XML</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #eef2f7;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
            }
            .container {
                background-color: #fff;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                text-align: center;
                width: 450px;
            }
            h1 {
                font-size: 28px;
                margin-bottom: 20px;
                color: #2c3e50;
            }
            label {
                font-size: 16px;
                color: #34495e;
            }
            input[type="file"],
            input[type="text"] {
                display: block;
                margin: 20px auto;
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
                width: 100%;
                font-size: 14px;
            }
            input[type="submit"] {
                background-color: #3498db;
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 5px;
                font-size: 16px;
                cursor: pointer;
                margin-top: 10px;
                width: 100%;
            }
            input[type="submit"]:hover {
                background-color: #2980b9;
            }
            .spinner {
                display: none;
                margin-top: 20px;
            }
            .spinner div {
                width: 18px;
                height: 18px;
                background-color: #3498db;
                border-radius: 100%;
                display: inline-block;
                animation: bounce 1.4s infinite ease-in-out both;
            }
            .spinner .bounce1 {
                animation-delay: -0.32s;
            }
            .spinner .bounce2 {
                animation-delay: -0.16s;
            }
            @keyframes bounce {
                0%, 100% {
                    transform: scale(0.0);
                }
                50% {
                    transform: scale(1.0);
                }
            }
            .progress-bar {
                width: 100%;
                background-color: #f3f3f3;
                border-radius: 5px;
                overflow: hidden;
                margin-top: 10px;
                display: none;
            }
            .progress {
                height: 12px;
                width: 0;
                background-color: #2ecc71;
                transition: width 0.4s;
            }
        </style>
        <script>
            function setActionAndKey(actionType, actionUrl) {
                const key = document.getElementById("key").value;
                if (!key) {
                    alert("Please enter a valid KEY.");
                    return false;
                }
                document.getElementById("action").value = actionType;  // Set action type
                document.getElementById("file-form").action = actionUrl + "?key=" + encodeURIComponent(key);  // Append KEY to URL
                return true;
            }

            function showSpinner() {
                document.getElementById("spinner").style.display = "block";
                document.getElementById("progress-bar").style.display = "block";
                setTimeout(() => {
                    document.querySelector(".progress").style.width = "100%";
                }, 100);
            }
        </script>
    </head>
    <body>
        <div class="container">
            <h1>Upload Excel File</h1>
            <form id="file-form" method="post" enctype="multipart/form-data" onsubmit="return setActionAndKey(actionType, actionUrl)">
                <label for="key">Enter KEY:</label>
                <input type="text" id="key" name="key" placeholder="Enter your API key here" required>

                <label for="file">Select Excel File:</label>
                <input type="file" id="file" name="file" accept=".xlsx" required>
                
                <input type="hidden" name="action" id="action">

                <input type="submit" value="Convert to XML" onclick="return setActionAndKey('excel_to_xml', '/api/excel-to-xml')">
            </form>

            <div class="progress-bar" id="progress-bar">
                <div class="progress"></div>
            </div>
            <div class="spinner" id="spinner">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
            </div>
        </div>
    </body>
    </html>
    """
    return func.HttpResponse(html_content, mimetype="text/html")


@app.route(route="excel-to-xml", methods=["GET", "POST"])
def excel_to_xml(req: func.HttpRequest) -> func.HttpResponse:

    user_key = req.params.get("key")
    if not user_key or user_key != AUTHENTICATION_SECRET:
        return func.HttpResponse("Unauthorized", status_code=401)

    try:

        uploaded_file = req.files.getlist("file")[0]

        if not uploaded_file:
            return func.HttpResponse("No files uploaded", status_code=400)

        file_name = uploaded_file.filename.lower()

        if file_name.endswith(".xlsx"):
            logging.info("Converting to XML.")
            xml_file_tree = create_xml_from_excel(
                excel_file_content=uploaded_file.read()
            )
        else:
            return func.HttpResponse(
                f"Unsupported file type: {file_name}. Only .xlsx allowed.",
                status_code=400,
            )

        saving_filename = file_name.split(".xlsx")[0]
        xml_buffer = io.BytesIO()
        xml_file_tree.write(xml_buffer, encoding="UTF-8", xml_declaration=True)
        xml_buffer.seek(0)
        return func.HttpResponse(
            xml_buffer.read(),
            mimetype="application/xml",
            headers={
                "Content-Disposition": f"attachment; filename={saving_filename}.xml"
            },
        )
    except Exception as e:
        logging.error(f"Error processing files: {e}")
        return func.HttpResponse(f"Error processing files: {str(e)}", status_code=500)


@app.route(route="download-excel", methods=["GET", "POST"])
def download_excel(req: func.HttpRequest) -> func.HttpResponse:

    user_key = req.params.get("key")
    if not user_key or user_key != AUTHENTICATION_SECRET:
        return func.HttpResponse("Unauthorized", status_code=401)

    try:
        uploaded_files = req.files.getlist("files")
        if not uploaded_files:
            return func.HttpResponse("No files uploaded", status_code=400)

        response_data = {}
        for file in uploaded_files:
            file_name = file.filename
            if file_name.lower().endswith((".pdf", ".jpg", ".jpeg", ".png")):
                response, _ = extractInvoice(file)
                logging.info("Extracted invoice data.")
                response_data[file_name] = response
            else:
                return func.HttpResponse(
                    f"Unsupported file type: {file_name}", status_code=400
                )

        # Convert the JSON response to a DataFrame
        logging.info("Converting to Excel.")
        data = []
        for file_name, invoice_data in response_data.items():
            row = {"Invoice File Name": file_name}
            invoice_data["VAT"] = invoice_data["VAT"]["total_value"]
            row.update(invoice_data)
            data.append(row)

        df = pd.DataFrame(data)

        # Save DataFrame to a BytesIO buffer
        logging.info("Saving to Excel.")
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, index=False, sheet_name="Invoices")

        # Set the content type to Excel and provide download
        output.seek(0)
        return func.HttpResponse(
            output.read(),
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": 'attachment; filename="invoices.xlsx"'},
        )

    except Exception as e:
        logging.error(f"Error processing files: {e}")
        return func.HttpResponse(f"Error processing files: {str(e)}", status_code=500)


@app.route(route="extract-invoice", methods=["POST"])
def extract_invoice(req: func.HttpRequest) -> func.HttpResponse:

    user_key = req.params.get("key")
    if not user_key or user_key != AUTHENTICATION_SECRET:  # Replace with your logic
        return func.HttpResponse("Unauthorized", status_code=401)

    try:
        # Retrieve the uploaded files (can be multiple)
        uploaded_files = req.files.getlist("files")
        if not uploaded_files:
            return func.HttpResponse("No files uploaded", status_code=400)

        response_data = {}

        # Process each uploaded file
        for file in uploaded_files:
            file_name = file.filename

            # Validate the file type (PDF, PNG, JPEG)
            if file_name.lower().endswith((".pdf", ".jpg", ".jpeg", ".png")):
                response, ocr_text = extractInvoice(file)
                logging.info("Extracted invoice data.")
                response_data[file_name] = response
            else:
                return func.HttpResponse(
                    f"Unsupported file type: {file_name}", status_code=400
                )

        # Return response as JSON
        return func.HttpResponse(json.dumps(response_data), mimetype="application/json")

    except Exception as e:
        logging.error(f"Error processing files: {e}")
        return func.HttpResponse(f"Error processing files: {e}", status_code=500)
