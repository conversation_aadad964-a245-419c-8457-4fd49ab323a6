import os

DISABLE_BASIC_AUTH = os.environ.get("DISABLE_BASIC_AUTH") or "true"
AZURE_VISION_AI_OCR_ENDPOINT = os.environ.get("NL_AZURE_VISION_AI_OCR_ENDPOINT")
AZURE_VISION_AI_API_KEYS = [
    i.strip() for i in os.environ.get("NL_AZURE_VISION_AI_API_KEYS").split(",")
]
POPPLER_PATH = os.environ.get("POPPLER_PATH") or ""
AZURE_OPEN_AI_ENDPOINT = os.environ.get("NL_AZURE_OPEN_AI_ENDPOINT")
AZURE_OPEN_AI_API_KEYS = [
    i for i in os.environ.get("NL_AZURE_OPEN_AI_API_KEYS").split(",")
]
AZURE_OPEN_AI_API_VERSION = os.environ.get("NL_AZURE_OPEN_AI_API_VERSION")
AZURE_OPEN_AI_DEPLOYMENT_NAME = os.environ.get("NL_AZURE_OPEN_AI_DEPLOYMENT_NAME")
AUTHENTICATION_SECRET = os.environ.get("NL_AUTHENTICATION_SECRET")

SYSTEM_PROMPT = """You are an Expert in Invoice Parsing and understand the fields from any type of Invoices. The language of the Invoice can be German or Spanish in some cases. Your task is to identify the language and accurately extract the required information from invoice and format it into a structured JSON template. Make sure the currency code is according to ISO 4217."""

INVOICE_FIELDS = """{
"Invoice No": "12345",
"Gross / Brutto": 1000.00,
"Net / Netto": 840.34,
"VAT": {
    "total_value": 159.66,
    "details": [
      {
        "rate": 7,
        "value": 29.66
      },
      {
        "rate": 19,
        "value": 130.00
      }
    ]
  },
"Currency": "USD",
"Issue date": "31-07-2024",
"Type": "Parking",
"Provider VAT ID": "VAT12345678",
"Provider name": "Provider Inc.",
"Provider address": "123 Provider Street, Provider City, Provider State, 12345",
"Provider country": "Provider Country"
}"""

DESCRIPTION = """Fields Description:
1. Invoice No: The unique identification number assigned to the invoice by the provider. It is also called "BNr" or "Beleg" or "Bon" or "Beleg-Nr" in German invoices.
2. Gross / Brutto: The total amount of the invoice including all taxes and additional charges.
3. Net / Netto: The amount of the invoice excluding any taxes or additional charges.
4. VAT: The total value of the Value Added Tax (VAT) for the invoice. Note that the VAT rate can vary on a single invoice, e.g., 7% for food and 19% for gas, but the absolute total value of VAT is required.
5. Currency: The type of currency used in the invoice (e.g., USD, EUR).
6. Issue date: The date on which the invoice was issued. It has to be in the format of DD-MM-YYYY.
7. Invoice Type: The type of Invoice out of the followings: Toll, Parking, Fuel, Diesel, Repair, AdBlue, Food, Accomodation, Other.
8. Provider VAT ID: The Value Added Tax identification number of the provider issuing the invoice without any space or symbol.
9. Provider name: The name of the company or individual who issued the invoice.
10. Provider address: The physical address of the provider issuing the invoice.
11. Provider country: The country in which the provider issuing the invoice is located. It can be written in German or other language, you will need to convert into English."""

CODES = {
    "Code": {
        "Toll": "4",
        "Parking": "3",
        "Fuel": "1",
        "Diesel": "1",
        "Repair": "3",
        "AdBlue": "1",
        "Food": "7",
        "Accomodation": "6",
        "Other": "10",
    },
    "Subcode": {
        "Toll": "4.1",
        "Parking": "3.4.3",
        "Fuel": "1.2.1",
        "Diesel": "1.1.2",
        "Repair": "3.4.2",
        "AdBlue": "1.13",
        "Food": "7.5",
        "Accomodation": "6.1",
        "Other": "10.1",
    },
}
