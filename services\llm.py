from openai import AzureOpenAI
import constants


def getResponse(query_prompt):

    client = AzureOpenAI(
        azure_endpoint=constants.AZURE_OPEN_AI_ENDPOINT,
        api_key=constants.AZURE_OPEN_AI_API_KEYS[0],
        api_version=constants.AZURE_OPEN_AI_API_VERSION,
    )

    response = client.chat.completions.create(
        model=constants.AZURE_OPEN_AI_DEPLOYMENT_NAME,
        response_format={"type": "json_object"},
        messages=[
            {"role": "system", "content": constants.SYSTEM_PROMPT},
            {"role": "user", "content": query_prompt},
        ],
    )

    return response


def preparePrompt(raw_text):
    prompt = (
        "Invoice Data: "
        + raw_text
        + " \n\n Read the above raw and unstructured Invoice text, extract the fields into following JSON Format based on provided fields description. Do not make up details that are not in the invoice data."
    )
    prompt = prompt + " \n\n " + constants.DESCRIPTION
    prompt = prompt + " \n\n JSON Example Format: \n\n " + constants.INVOICE_FIELDS
    return prompt
