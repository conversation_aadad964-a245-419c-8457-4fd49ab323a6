import fitz
import constants
import io
import logging
from ocr import extractAzureOCRText
from PIL import Image, ImageFilter
from io import BytesIO


def sort_bounding_boxes(bb):
    # Sort by y-coordinate, then by x-coordinate
    bb_sorted = sorted(bb, key=lambda x: (x[0][1], x[0][0]))
    return bb_sorted


def getImage(page):

    # Set the desired DPI
    desired_dpi = 400
    zoom = desired_dpi / 72  # Calculate zoom factor

    # Transformation matrix
    mat = fitz.Matrix(zoom, zoom)

    pix = page.get_pixmap(matrix=mat)

    image_bytes = pix.tobytes()
    img = Image.open(io.BytesIO(image_bytes))

    # Apply anti-aliasing filter
    img = img.filter(ImageFilter.SMOOTH)

    return img


def processPDFTextPages(pdf_file_contents):
    ocr_pages = []
    page_numbers = []
    text_pages = {}

    file = io.BytesIO(pdf_file_contents)

    with fitz.open(stream=file, filetype="pdf") as pdf:

        for page_num in range(pdf.page_count):
            key = str(page_num + 1)
            page = pdf.load_page(page_num)

            words = page.get_text(option="words")

            bounding_boxes = []

            for word in words:
                x0 = int(word[0])
                y0 = int(word[1])
                x1 = int(word[2])
                y1 = int(word[3])
                word_text = word[4]

                bounding_boxes.append([[x0, y0, x1, y1], word_text])

            data = {
                "content": page.get_text().strip(),
                "info": sort_bounding_boxes(bounding_boxes),
                "image_size": {
                    "width": int(page.rect.width),
                    "height": int(page.rect.height),
                },
                "extraction": "text",
            }

            if data["content"] == "" or "$image$" in data["content"]:
                ocr_pages.append(getImage(page))
                page_numbers.append(page_num + 1)
            else:
                text_pages[key] = data

    return text_pages, ocr_pages, page_numbers


def getOCRed(pages_text, ocr_pages, page_numbers):

    if len(ocr_pages) != 0:

        logging.info(str(len(ocr_pages)) + " pages to be OCRed.")

        ocr_pages_text = extractAzureOCRText(ocr_pages, page_numbers)

        logging.info("OCR text extracted.")

        for key in ocr_pages_text:
            pages_text[str(key)] = ocr_pages_text[key]

    raw_text = getRawPlainText(pages_text)

    return pages_text, raw_text


def load_pdf(pdf_file_contents):

    logging.info("Calling processPDFTextPages.")

    pages_text, ocr_pages, page_numbers = processPDFTextPages(pdf_file_contents)

    logging.info("Text Based Pages: " + str(len(pages_text)))
    logging.info("Image Based Pages: " + str(len(ocr_pages)))

    return pages_text, ocr_pages, page_numbers


def load_image(file_contents):

    ocr_pages = [Image.open(BytesIO(file_contents))]
    page_numbers = [1]
    pages_text = {}

    return pages_text, ocr_pages, page_numbers


def getRawPlainText(pages_text):

    raw_text = ""

    for key in pages_text:
        raw_text += pages_text[key]["content"] + " \n\n "

    return raw_text.strip()
