"""from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.exceptions import HTTPException
import logging
from routes.extract import extractInvoice
import uvicorn

app = FastAPI()

@app.get("/upload", response_class=HTMLResponse)
async def get_upload_form():
    html_content = None
    return HTMLResponse(content=html_content)

@app.post("/extract-invoice")
async def extract_invoice(files: list[UploadFile] = File(...)):
    try:
        if not files:
            raise HTTPException(status_code=400, detail="No files uploaded")

        response_data = {}

        # Process each uploaded file
        for file in files:
            file_name = file.filename

            # Validate the file type (PDF, PNG, JPEG)
            if file_name.lower().endswith(('.pdf', '.jpg', '.jpeg', '.png')):
                response, ocr_text = extractInvoice(file.file)
                logging.info("Extracted invoice data.")
                response_data[file_name] = response
            else:
                raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_name}")

        # Return response as JSON
        return JSONResponse(content=response_data)

    except Exception as e:
        logging.error(f"Error processing files: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing files: {e}")
    
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)"""